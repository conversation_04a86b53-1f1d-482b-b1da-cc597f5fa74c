kind: pipeline
type: docker
name: deploy-on-main

trigger:
  branch:
    - main
  event:
    - push

steps:
  - name: build-and-run
    image: docker:27-cli
    environment:
      SOME_ENV: SomeEnv
      SOME_SECRET:
        from_secret: SOME_SECRET
    commands:
      - docker compose -f compose.yaml -f drone.compose.yaml down --remove-orphans
      - docker compose -f compose.yaml -f drone.compose.yaml up -d --build

