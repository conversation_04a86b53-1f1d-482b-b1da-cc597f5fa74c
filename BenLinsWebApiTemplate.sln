
Microsoft Visual Studio Solution File, Format Version 12.00
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{F446D0BB-0C98-48AB-81B4-45C6843F7FC0}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "BenLinsWebApiTemplate.WebApi", "src\BenLinsWebApiTemplate.WebApi\BenLinsWebApiTemplate.WebApi.csproj", "{03530925-7270-4BD8-8BDE-64B9FFA43EF7}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{B76464C5-E170-4417-8424-C50751C90510}"
	ProjectSection(SolutionItems) = preProject
		compose.yaml = compose.yaml
#if (useDroneCiCd)
        .drone.yml = .drone.yml
		drone.compose.yaml = drone.compose.yaml
#endif
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "BenLinsWebApiTemplate.Domain", "src\BenLinsWebApiTemplate.Domain\BenLinsWebApiTemplate.Domain.csproj", "{226BA609-A893-43E7-A632-B351B8614D5B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "BenLinsWebApiTemplate.Application", "src\BenLinsWebApiTemplate.Application\BenLinsWebApiTemplate.Application.csproj", "{D283951A-6363-4F9B-BE74-CC75CFB39E96}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "BenLinsWebApiTemplate.Infrastructure", "src\BenLinsWebApiTemplate.Infrastructure\BenLinsWebApiTemplate.Infrastructure.csproj", "{E7E067AC-CE4C-456D-BC7B-0A99FC325FF7}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "BenLinsWebApiTemplate.Contracts", "src\BenLinsWebApiTemplate.Contracts\BenLinsWebApiTemplate.Contracts.csproj", "{2C620BFB-BEA4-447F-BDB6-55ABF4F95E9E}"
EndProject
#if (createTests)
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{C8E44B5B-6B38-4CB9-AA87-6D5F0D895D1B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "BenLinsWebApiTemplate.Domain.Tests", "tests\BenLinsWebApiTemplate.Domain.Tests\BenLinsWebApiTemplate.Domain.Tests.csproj", "{23615C44-B3FF-4F64-B73F-7FD01EC39BE2}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "BenLinsWebApiTemplate.Application.Tests", "tests\BenLinsWebApiTemplate.Application.Tests\BenLinsWebApiTemplate.Application.Tests.csproj", "{8930DC49-24D7-4B55-A3B9-3D93FEC3EB57}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "BenLinsWebApiTemplate.Infrastructure.Tests", "tests\BenLinsWebApiTemplate.Infrastructure.Tests\BenLinsWebApiTemplate.Infrastructure.Tests.csproj", "{172FB632-4422-44F4-92B9-9AC2A92C6A2B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "BenLinsWebApiTemplate.WebApi.Tests", "tests\BenLinsWebApiTemplate.WebApi.Tests\BenLinsWebApiTemplate.WebApi.Tests.csproj", "{E233D6F8-CE3E-4D4B-A901-B679854778D2}"
EndProject
#endif
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{03530925-7270-4BD8-8BDE-64B9FFA43EF7} = {F446D0BB-0C98-48AB-81B4-45C6843F7FC0}
		{226BA609-A893-43E7-A632-B351B8614D5B} = {F446D0BB-0C98-48AB-81B4-45C6843F7FC0}
		{D283951A-6363-4F9B-BE74-CC75CFB39E96} = {F446D0BB-0C98-48AB-81B4-45C6843F7FC0}
		{E7E067AC-CE4C-456D-BC7B-0A99FC325FF7} = {F446D0BB-0C98-48AB-81B4-45C6843F7FC0}
		{2C620BFB-BEA4-447F-BDB6-55ABF4F95E9E} = {F446D0BB-0C98-48AB-81B4-45C6843F7FC0}
#if (createTests)
		{23615C44-B3FF-4F64-B73F-7FD01EC39BE2} = {C8E44B5B-6B38-4CB9-AA87-6D5F0D895D1B}
		{8930DC49-24D7-4B55-A3B9-3D93FEC3EB57} = {C8E44B5B-6B38-4CB9-AA87-6D5F0D895D1B}
		{172FB632-4422-44F4-92B9-9AC2A92C6A2B} = {C8E44B5B-6B38-4CB9-AA87-6D5F0D895D1B}
		{E233D6F8-CE3E-4D4B-A901-B679854778D2} = {C8E44B5B-6B38-4CB9-AA87-6D5F0D895D1B}
#endif
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{03530925-7270-4BD8-8BDE-64B9FFA43EF7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{03530925-7270-4BD8-8BDE-64B9FFA43EF7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{03530925-7270-4BD8-8BDE-64B9FFA43EF7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{03530925-7270-4BD8-8BDE-64B9FFA43EF7}.Release|Any CPU.Build.0 = Release|Any CPU
		{226BA609-A893-43E7-A632-B351B8614D5B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{226BA609-A893-43E7-A632-B351B8614D5B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{226BA609-A893-43E7-A632-B351B8614D5B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{226BA609-A893-43E7-A632-B351B8614D5B}.Release|Any CPU.Build.0 = Release|Any CPU
		{D283951A-6363-4F9B-BE74-CC75CFB39E96}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D283951A-6363-4F9B-BE74-CC75CFB39E96}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D283951A-6363-4F9B-BE74-CC75CFB39E96}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D283951A-6363-4F9B-BE74-CC75CFB39E96}.Release|Any CPU.Build.0 = Release|Any CPU
		{E7E067AC-CE4C-456D-BC7B-0A99FC325FF7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E7E067AC-CE4C-456D-BC7B-0A99FC325FF7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E7E067AC-CE4C-456D-BC7B-0A99FC325FF7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E7E067AC-CE4C-456D-BC7B-0A99FC325FF7}.Release|Any CPU.Build.0 = Release|Any CPU
		{2C620BFB-BEA4-447F-BDB6-55ABF4F95E9E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2C620BFB-BEA4-447F-BDB6-55ABF4F95E9E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2C620BFB-BEA4-447F-BDB6-55ABF4F95E9E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2C620BFB-BEA4-447F-BDB6-55ABF4F95E9E}.Release|Any CPU.Build.0 = Release|Any CPU
#if (createTests)
		{23615C44-B3FF-4F64-B73F-7FD01EC39BE2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{23615C44-B3FF-4F64-B73F-7FD01EC39BE2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{23615C44-B3FF-4F64-B73F-7FD01EC39BE2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{23615C44-B3FF-4F64-B73F-7FD01EC39BE2}.Release|Any CPU.Build.0 = Release|Any CPU
		{8930DC49-24D7-4B55-A3B9-3D93FEC3EB57}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8930DC49-24D7-4B55-A3B9-3D93FEC3EB57}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8930DC49-24D7-4B55-A3B9-3D93FEC3EB57}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8930DC49-24D7-4B55-A3B9-3D93FEC3EB57}.Release|Any CPU.Build.0 = Release|Any CPU
		{172FB632-4422-44F4-92B9-9AC2A92C6A2B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{172FB632-4422-44F4-92B9-9AC2A92C6A2B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{172FB632-4422-44F4-92B9-9AC2A92C6A2B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{172FB632-4422-44F4-92B9-9AC2A92C6A2B}.Release|Any CPU.Build.0 = Release|Any CPU
		{E233D6F8-CE3E-4D4B-A901-B679854778D2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E233D6F8-CE3E-4D4B-A901-B679854778D2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E233D6F8-CE3E-4D4B-A901-B679854778D2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E233D6F8-CE3E-4D4B-A901-B679854778D2}.Release|Any CPU.Build.0 = Release|Any CPU
#endif
	EndGlobalSection
EndGlobal
