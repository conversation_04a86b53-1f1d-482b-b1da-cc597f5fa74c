using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
//#if (useDatabase != "none")
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
//#endif
//#if (useDatabaseMongo)
using MongoDB.Driver;
//#endif
//#if (useDatabase != "none")
using BenLinsWebApiTemplate.Infrastructure.Configurations;
using BenLinsWebApiTemplate.Infrastructure.Persistence.Ef;
using BenLinsWebApiTemplate.Infrastructure.Persistence.Ef.Hosting;
//#endif

namespace BenLinsWebApiTemplate.Infrastructure.Extensions;

/// <summary>
/// Методы расширения для регистрации сервисов уровня инфраструктуры в контейнере зависимостей.
/// Предоставляет централизованную точку для настройки всех компонентов инфраструктуры.
/// </summary>
public static class InfrastructureServiceCollectionExtensions
{
    /// <summary>
    /// Регистрирует сервисы инфраструктуры в контейнере зависимостей.
    /// </summary>
    public static IServiceCollection AddInfrastructure(this IServiceCollection services, IConfiguration configuration)
    {
//#if (useDatabaseMongo)
        services.AddMongoClient();
//#endif
//#if (useDatabase != "none")
        services.AddBenLinsWebApiTemplateDb();
//#endif
        
        return services;
    }
    
//#if (useDatabaseMongo)
    /// <summary>
    /// Регистрирует клиент MongoDB и конфигурацию персистентности.
    /// </summary>
    private static IServiceCollection AddMongoClient(this IServiceCollection services)
    {
        services
            .AddOptions<PersistenceConfig>()
            .BindConfiguration(nameof(PersistenceConfig))
            .ValidateDataAnnotations()
            .ValidateOnStart();

        services.AddSingleton<IMongoClient>(serviceProvider =>
        {
            var persistenceConfig = serviceProvider.GetRequiredService<IOptions<PersistenceConfig>>().Value;

            return new MongoClient(persistenceConfig.MongoDbConnectionString);
        });

        return services;
    }
//#endif
//#if (useDatabase != "none")
    /// <summary>
    /// Регистрирует контекст базы данных и сервис её инициализации.
    /// </summary>
    private static IServiceCollection AddBenLinsWebApiTemplateDb(this IServiceCollection services)
    {
//#if (useDatabasePostgres)
        services
            .AddOptions<PersistenceConfig>()
            .BindConfiguration(nameof(PersistenceConfig))
            .ValidateDataAnnotations()
            .ValidateOnStart();
//#endif
        services.AddOptions<BenLinsWebApiTemplateDbConfig>()
            .BindConfiguration(nameof(PersistenceConfig) + ":" + nameof(BenLinsWebApiTemplateDbConfig))
            .ValidateDataAnnotations()
            .ValidateOnStart();

//#if (useDatabaseMongo)
        services.AddDbContext<BenLinsWebApiTemplateDbContext>((serviceProvider, options) =>
        {
            var client = serviceProvider.GetRequiredService<IMongoClient>();
            var gameDataDbConfig = serviceProvider.GetRequiredService<IOptions<BenLinsWebApiTemplateDbConfig>>().Value;

            options.UseMongoDB(client, gameDataDbConfig.DatabaseName);
        });
//#elif (useDatabasePostgres)
        services.AddDbContextPool<BenLinsWebApiTemplateDbContext>((serviceProvider, options) =>
        {
            var persistenceConfig = serviceProvider.GetRequiredService<IOptions<PersistenceConfig>>().Value;
            
            options.UseNpgsql(persistenceConfig.PostgresConnectionString);
        });
//#endif

        services.AddHostedService<BenLinsWebApiTemplateDbInitializationHostedService>();

        return services;
    }
//#endif
}
