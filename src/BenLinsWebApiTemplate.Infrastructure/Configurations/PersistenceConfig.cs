using System.ComponentModel.DataAnnotations;

namespace BenLinsWebApiTemplate.Infrastructure.Configurations;

public class PersistenceConfig
{
//#if (useDatabaseMongo)
    /// <summary>
    /// Строка подключения к MongoDB.
    /// Используется для создания клиента MongoDB, который затем применяется
    /// для всех MongoDB баз данных в системе.
    /// </summary>
    [Required]
    public string MongoDbConnectionString { get; set; } = null!;
//#elif (useDatabasePostgres)
    /// <summary>
    /// Строка подключения к PostgreSQL.
    /// </summary>
    [Required]
    public string PostgresConnectionString { get; set; } = null!;
//#endif

    /// <summary>
    /// Конфигурация базы данных.
    /// </summary>
    public BenLinsWebApiTemplateDbConfig BenLinsWebApiTemplateDbConfig { get; set; } = null!;
}
