using System.ComponentModel.DataAnnotations;

namespace BenLinsWebApiTemplate.Infrastructure.Configurations;

/// <summary>
/// Конфигурация базы данных.
/// </summary>
public class BenLinsWebApiTemplateDbConfig
{
//#if (useDatabaseMongo)    
    /// <summary>
    /// Имя базы данных.
    /// </summary>
    [Required]
    public string DatabaseName { get; set; } = "BenLinsWebApiTemplate";
    
//#endif
    /// <summary>
    /// Путь к JSON-файлу с начальными данными для базы данных.
    /// Может быть абсолютным или относительным к директории приложения.
    /// Используется при первом создании БД для загрузки базовых данных.
    /// </summary>
    [Required]
    public string DbSeedJsonPath { get; set; } = "./seed/BenLinsWebApiTemplateDbSeed.json";
}
