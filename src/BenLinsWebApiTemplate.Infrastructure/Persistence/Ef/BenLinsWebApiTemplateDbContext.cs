using Microsoft.EntityFrameworkCore;

namespace BenLinsWebApiTemplate.Infrastructure.Persistence.Ef;

/// <summary>
/// Контекст Entity Framework для базы данных.
/// </summary>
public class BenLinsWebApiTemplateDbContext : DbContext
{
    /// <summary>
    /// Инициализирует новый экземпляр контекста базы данных.
    /// Он пустой, так как Entity Framework требует конструктор с параметром options.
    /// </summary>
    public BenLinsWebApiTemplateDbContext(DbContextOptions<BenLinsWebApiTemplateDbContext> options) : base(options) { }
    
    /// <summary>
    /// Настраивает модель данных при создании контекста.
    /// Применяет конфигурации Entity Framework для всех сущностей.
    /// </summary>
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);
    }
}
