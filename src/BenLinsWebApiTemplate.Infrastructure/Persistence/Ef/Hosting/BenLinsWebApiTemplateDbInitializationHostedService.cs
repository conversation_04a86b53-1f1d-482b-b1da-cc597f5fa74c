using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using BenLinsWebApiTemplate.Infrastructure.Configurations;
using BenLinsWebApiTemplate.Infrastructure.Persistence.Ef.Seed;

namespace BenLinsWebApiTemplate.Infrastructure.Persistence.Ef.Hosting;

/// <summary>
/// Фоновый сервис для инициализации базы данных при запуске приложения.
/// Создает БД если она не существует и заполняет её начальными данными.
/// </summary>
public class BenLinsWebApiTemplateDbInitializationHostedService(
    IServiceProvider serviceProvider,
    IOptions<BenLinsWebApiTemplateDbConfig> dbConfig,
    ILogger<BenLinsWebApiTemplateDbInitializationHostedService> logger)
    : IHostedService
{
    /// <summary>
    /// Выполняется при запуске приложения.
    /// Создает базу данных если она не существует и заполняет её начальными данными.
    /// </summary>
    public async Task StartAsync(CancellationToken cancellationToken)
    {
        logger.LogInformation("Начало инициализации базы данных");

        using var scope = serviceProvider.CreateScope();

        var db = scope.ServiceProvider.GetRequiredService<BenLinsWebApiTemplateDbContext>();

        var justCreated = await db.Database.EnsureCreatedAsync(cancellationToken);

        if (justCreated)
        {
            logger.LogInformation("База данных создана. Начинается заполнение начальными данными");
            await BenLinsWebApiTemplateDbSeeder.Seed(db, dbConfig.Value, cancellationToken);
            logger.LogInformation("База данных успешно заполнена начальными данными");
        }
        else
        {
            logger.LogInformation("База данных уже существует, заполнение начальными данными пропущено");
        }

        logger.LogInformation("Инициализация базы данных завершена");
    }

    /// <summary>
    /// Выполняется при остановке приложения. Не требует дополнительных действий.
    /// </summary>
    public Task StopAsync(CancellationToken cancellationToken) => Task.CompletedTask;
}
