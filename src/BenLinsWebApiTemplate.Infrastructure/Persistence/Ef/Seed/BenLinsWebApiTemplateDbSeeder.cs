using Newtonsoft.Json;
using BenLinsWebApiTemplate.Infrastructure.Configurations;

namespace BenLinsWebApiTemplate.Infrastructure.Persistence.Ef.Seed;

/// <summary>
/// Статический класс для заполнения базы данных начальными значениями.
/// Загружает данные из JSON-файла и добавляет их в базу данных.
/// </summary>
public class BenLinsWebApiTemplateDbSeeder
{
    /// <summary>
    /// Заполняет базу данных начальными значениями из JSON-файла.
    /// Если файл не существует, операция пропускается без ошибок.
    /// </summary>
    public static async Task Seed(BenLinsWebApiTemplateDbContext db, BenLinsWebApiTemplateDbConfig config, CancellationToken ct = default)
    {
        var path = Path.IsPathRooted(config.DbSeedJsonPath)
            ? config.DbSeedJsonPath
            : Path.Combine(AppContext.BaseDirectory, config.DbSeedJsonPath);

        if (!File.Exists(path))
            return;

        var json = await File.ReadAllTextAsync(path, ct);
        var seed = JsonConvert.DeserializeObject<BenLinsWebApiTemplateDbSeed>(json);
        
        // TODO: Записать начальные данные в базу данных

        await db.SaveChangesAsync(ct);
    }
}
