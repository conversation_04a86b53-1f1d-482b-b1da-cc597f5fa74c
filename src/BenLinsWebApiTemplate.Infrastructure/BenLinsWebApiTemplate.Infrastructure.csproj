<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <ProjectReference Include="..\BenLinsWebApiTemplate.Application\BenLinsWebApiTemplate.Application.csproj" />
      <ProjectReference Include="..\BenLinsWebApiTemplate.Domain\BenLinsWebApiTemplate.Domain.csproj" />
    </ItemGroup>

    <ItemGroup>
      <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.8" />
      <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.8" />
      <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="9.0.8" />
<!--#if (useDatabase != "none")-->
      <PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" Version="9.0.8" />
      <PackageReference Include="Microsoft.Extensions.Options.DataAnnotations" Version="9.0.8" />
      <PackageReference Include="Newtonsoft.Json" Version="13.0.1" />
<!--#endif-->
<!--#if (useDatabaseMongo)-->
      <PackageReference Include="MongoDB.EntityFrameworkCore" Version="9.0.0" />
<!--#elif (useDatabasePostgres)-->
      <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.4" />
<!--#endif-->
    </ItemGroup>

</Project>
