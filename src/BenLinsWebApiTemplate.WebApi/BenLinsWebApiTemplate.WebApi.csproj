<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
        <PreserveCompilationContext>true</PreserveCompilationContext>
        <CopyOutputSymbolsToPublishDirectory>true</CopyOutputSymbolsToPublishDirectory>
        <CopyOutputSymbolsToBuildDirectory>true</CopyOutputSymbolsToBuildDirectory>
    </PropertyGroup>

    <ItemGroup>
      <Content Include="..\..\.dockerignore">
        <Link>.dockerignore</Link>
      </Content>
    </ItemGroup>

    <ItemGroup>
      <None Remove="Properties\launchSettings.json" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\BenLinsWebApiTemplate.Application\BenLinsWebApiTemplate.Application.csproj" />
      <ProjectReference Include="..\BenLinsWebApiTemplate.Contracts\BenLinsWebApiTemplate.Contracts.csproj" />
      <ProjectReference Include="..\BenLinsWebApiTemplate.Infrastructure\BenLinsWebApiTemplate.Infrastructure.csproj" />
    </ItemGroup>

    <ItemGroup>
      <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
      <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
      <PackageReference Include="Serilog.Sinks.Seq" Version="9.0.0" />
    </ItemGroup>

</Project>
