{
  "AllowedHosts": "*",
  "Serilog": {
    "MinimumLevel": {
      "Default": "Information",
      "Override": {
        "Microsoft": "Warning",
        "Microsoft.Hosting.Lifetime": "Information",
        "Microsoft.AspNetCore.Hosting": "Information"
      }
    },
    "Enrich": [ "FromLogContext", "WithMachineName", "WithThreadId", "WithProcessId" ],
    "WriteTo": [
      { "Name": "Console" },
      {
        "Name": "Seq",
        "Args": {
          
        }
      }
    ],
    "Properties": {
      "Application": "BenLinsWebApiTemplate"
    }
//#if (useDatabase == "none")
  }
//#else
  },
//#endif
//#if (useDatabase != "none")  
  "PersistenceConfig" : {
    "BenLinsWebApiTemplateDbConfig": {
      "DatabaseName": "BenLinsWebApiTemplate",
      "GameDataDbSeedJsonPath": "./seed/BenLinsWebApiTemplateDbSeed.json"
    }
  }
//#endif
}
