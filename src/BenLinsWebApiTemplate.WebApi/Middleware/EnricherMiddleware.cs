using Serilog.Context;

namespace BenLinsWebApiTemplate.WebApi.Middleware;

/// <summary>
/// Сервис для добавления информации в контекст логирования, на этапе поступления HTTP-запроса.
/// </summary>
///
/// <remarks>
/// Для получения информации о пользователе из контекста HTTP-запроса,
/// подключение <see cref="EnricherMiddleware"/> должно быть выполнено после аутентификации и авторизации,
/// и до app.UseSerilogRequestLogging(), чтобы финальный лог "responded 200 in ..." тоже содержал эти поля.
/// <code>
/// app.UseAuthentication();
/// app.UseAuthorization();
/// 
/// app.UseMiddleware&lt;EnricherMiddleware&gt;();
/// 
/// app.UseSerilogRequestLogging();
/// </code>
/// </remarks>
public sealed class EnricherMiddleware(RequestDelegate next, ILogger<EnricherMiddleware> logger)
{
    public async Task Invoke(HttpContext ctx)
    {
        logger.LogWarning("Добавляем UserId в контекст логирования в целях демонстрации работы EnricherMiddleware");
        
        var userId =
            ctx.User.FindFirst("sub")?.Value
            ?? "anonymous";

        var disposables = new[]
        {
            LogContext.PushProperty("UserId", userId)
        };

        try
        {
            await next(ctx);
        }
        finally
        {
            foreach (var d in disposables.Reverse()) 
                d.Dispose();
        }
    }
}
