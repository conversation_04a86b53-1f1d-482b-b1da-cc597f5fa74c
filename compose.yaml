name: BenLinsWebApiTemplate

services:
  
  BenLinsWebApiTemplate-WebApi:
    image: benlinswebapitemplate.webapi
    build:
      context: .
      dockerfile: src/BenLinsWebApiTemplate.WebApi/Dockerfile
    restart: unless-stopped
    ports:
      - 25565:8080
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
#if (useDatabaseMongo)
      - MONGODB_CONNECTION_STRING=mongodb://BenLinsWebApiTemplate-MongoDb:27017/?replicaSet=rs0
#elif (useDatabasePostgres)
      - POSTGRES_CONNECTION_STRING=Host=BenLinsWebApiTemplate-Postgres;Port=5432;Database=benlinswebapitemplate;Username=dbUser;Password=dbUserPassword
#endif
#if (useDroneCiCd)
      - SOME_ENV=${SOME_ENV}
      - SOME_SECRET=${SOME_SECRET}
#endif
#if (useDatabaseMongo)
    depends_on:
      BenLinsWebApiTemplate-MongoDb:
        condition: service_healthy
#elif (useDatabasePostgres)
    depends_on:
      BenLinsWebApiTemplate-Postgres:
        condition: service_healthy
#endif
      
#if (useDatabaseMongo)
  BenLinsWebApiTemplate-MongoDb:
    image: mongo:latest
    restart: unless-stopped
    volumes:
      - ./volumes/MongoDb/data:/data/db
    command: >
      bash -lc '
        set -e
        mongod --replSet rs0 --bind_ip_all &
        pid=$!
        # Ждём подъёма сервера
        for i in {1..60}; do
          mongosh --quiet --eval "db.adminCommand({ ping: 1 })" && break || sleep 1
        done
        # Инициализируем ReplicaSet, если он ещё не инициализирован
        mongosh --quiet --eval "
          try {
            rs.status()
          } catch (e) {
            rs.initiate({
              _id: \"rs0\",
              members: [{ _id: 0, host: \"BenLinsWebApiTemplate-MongoDb:27017\" }]
            })
          }
        "
        wait $pid
      '
    healthcheck:
      test: [ "CMD-SHELL", "mongosh --quiet --eval \"db.hello().isWritablePrimary\" | grep true" ]
      interval: 5s
      timeout: 5s
      retries: 24
      start_period: 120s
#elif (useDatabasePostgres)
  BenLinsWebApiTemplate-Postgres:
    image: postgres:latest
    restart: unless-stopped
    environment:
      POSTGRES_USER: dbUser
      POSTGRES_PASSWORD: dbUserPassword
      PGDATA: /data/postgres
    volumes:
      - ./volumes/Postgres/data:/data/postgres
    healthcheck:
      test: [ "CMD-SHELL", "pg_isready -U dbUser -d benlinswebapitemplate -h 127.0.0.1 -p 5432" ]
      interval: 5s
      timeout: 3s
      retries: 10
#endif

